// components/home/<USER>
'use client';

import { useEffect, useState } from 'react';
import { useInView } from 'react-intersection-observer';
import dynamic from 'next/dynamic';

// ✅ CRITICAL: SSR-Safe Hero component with consent integration
import HeroWrapper from '@/components/home/<USER>';
import HomePageScrollHandler from './HomePageScrollHandler';

// ✅ STAGE 1: Below-the-fold Hero content - loads only when needed
const HeroBelowFold = dynamic(() => import('@/components/home/<USER>'), {
  loading: () => (
    <div className="h-screen bg-gradient-to-b from-slate-800/20 to-slate-900/20 animate-pulse" />
  ),
  ssr: false
});

// ✅ STAGE 2: Essential content - loads only when needed
const HomepageIntroduction = dynamic(
  () => import('@/components/home/<USER>'),
  {
    loading: () => (
      <div className="h-[40vh] bg-gradient-to-b from-white/5 to-white/10 animate-pulse rounded-xl mx-4" />
    ),
    ssr: false
  }
);

const HowItWorksProcess = dynamic(
  () =>
    import('./HowItWorksProcess').then((mod) => ({
      default: mod.HowItWorksProcess
    })),
  {
    loading: () => (
      <div className="h-[60vh] bg-gradient-to-b from-white/5 to-white/10 animate-pulse rounded-xl mx-4" />
    ),
    ssr: false
  }
);

// ✅ STAGE 3: Secondary content - loads only when in viewport
const PromoSection = dynamic(
  () => import('./PromoSection').then((mod) => ({ default: mod.PromoSection })),
  {
    loading: () => (
      <div className="h-[40vh] bg-gradient-to-b from-white/5 to-white/10 animate-pulse rounded-xl mx-4" />
    ),
    ssr: false
  }
);

const ReportsShowcase = dynamic(
  () =>
    import('./ReportsShowcase').then((mod) => ({
      default: mod.ReportsShowcase
    })),
  {
    loading: () => (
      <div className="h-[60vh] bg-gradient-to-b from-white/5 to-white/10 animate-pulse rounded-xl mx-4" />
    ),
    ssr: false
  }
);

// ✅ STAGE 4: Heavy feature content - loads only when needed
const FeaturesHowItWorksSection = dynamic(
  () =>
    import('./HowItWorks').then((mod) => ({ default: mod.HowItWorksSection })),
  {
    loading: () => (
      <div className="h-[60vh] bg-gradient-to-b from-white/5 to-white/10 animate-pulse rounded-xl mx-4" />
    ),
    ssr: false
  }
);

const FeaturesSection = dynamic(
  () => import('./Features').then((mod) => ({ default: mod.FeaturesSection })),
  {
    loading: () => (
      <div className="h-[60vh] bg-gradient-to-b from-white/5 to-white/10 animate-pulse rounded-xl mx-4" />
    ),
    ssr: false
  }
);

// ✅ STAGE 5: Bottom content - loads only when needed
const LatestArticles = dynamic(
  () =>
    import('./LatestArticles').then((mod) => ({ default: mod.LatestArticles })),
  {
    loading: () => (
      <div className="h-[60vh] bg-gradient-to-b from-white/5 to-white/10 animate-pulse rounded-xl mx-4" />
    ),
    ssr: false
  }
);

const HomepageFAQSection = dynamic(
  () =>
    import('./HomepageFAQSection').then((mod) => ({
      default: mod.HomepageFAQSection
    })),
  {
    loading: () => (
      <div className="h-[60vh] bg-gradient-to-b from-white/5 to-white/10 animate-pulse rounded-xl mx-4" />
    ),
    ssr: false
  }
);

const PricingSection = dynamic(
  () =>
    import('./PricingSection').then((mod) => ({ default: mod.PricingSection })),
  {
    loading: () => (
      <div className="h-[60vh] bg-gradient-to-b from-white/5 to-white/10 animate-pulse rounded-xl mx-4" />
    ),
    ssr: false
  }
);

const FinalCTASection = dynamic(
  () => import('./FinalCTA').then((mod) => ({ default: mod.FinalCTASection })),
  {
    loading: () => (
      <div className="h-[40vh] bg-gradient-to-b from-white/5 to-white/10 animate-pulse rounded-xl mx-4" />
    ),
    ssr: false
  }
);

// ✅ STAGE 6: Non-critical scripts - loads only when needed
const EnhancedSEOScript = dynamic(() => import('../seo/EnhancedSEOScript'), {
  ssr: false
});

// ✅ Reusable LazySection component for ultra-aggressive loading
const LazySection = ({
  children,
  fallback,
  rootMargin = '400px 0px',
  className = ''
}: {
  children: React.ReactNode;
  fallback: React.ReactNode;
  rootMargin?: string;
  className?: string;
}) => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    rootMargin,
    threshold: 0
  });

  return (
    <div ref={ref} className={className}>
      {inView ? children : fallback}
    </div>
  );
};

// ✅ Stage 1: Below-the-fold hero with delayed loading
const Stage1Content = () => {
  const [shouldLoad, setShouldLoad] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setShouldLoad(true), 800);
    return () => clearTimeout(timer);
  }, []);

  if (!shouldLoad) {
    return (
      <div className="h-screen bg-gradient-to-b from-slate-800/20 to-slate-900/20 animate-pulse" />
    );
  }

  return <HeroBelowFold />;
};

// ✅ Stage 2: Essential content with delayed loading
const Stage2Content = () => {
  const [shouldLoad, setShouldLoad] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setShouldLoad(true), 1500);
    return () => clearTimeout(timer);
  }, []);

  if (!shouldLoad) {
    return (
      <>
        <div className="h-[40vh] bg-gradient-to-b from-white/5 to-white/10 animate-pulse rounded-xl mx-4 mb-8" />
        <div className="h-[60vh] bg-gradient-to-b from-white/5 to-white/10 animate-pulse rounded-xl mx-4" />
      </>
    );
  }

  return (
    <>
      <HomepageIntroduction />
      <HowItWorksProcess />
    </>
  );
};

// ✅ Stage 3: Secondary content with scroll-based and time-based loading
const Stage3Content = () => {
  const [shouldLoad, setShouldLoad] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setShouldLoad(true), 3000);

    const handleScroll = () => {
      if (window.scrollY > 400) {
        setShouldLoad(true);
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      clearTimeout(timer);
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  if (!shouldLoad) {
    return (
      <>
        <div className="h-[40vh] bg-gradient-to-b from-white/5 to-white/10 animate-pulse rounded-xl mx-4 mb-8" />
        <div className="h-[60vh] bg-gradient-to-b from-white/5 to-white/10 animate-pulse rounded-xl mx-4" />
      </>
    );
  }

  return (
    <>
      <LazySection
        fallback={
          <div className="h-[40vh] bg-gradient-to-b from-white/5 to-white/10 animate-pulse rounded-xl mx-4" />
        }
        rootMargin="400px 0px"
      >
        <PromoSection />
      </LazySection>

      <LazySection
        fallback={
          <div className="h-[60vh] bg-gradient-to-b from-white/5 to-white/10 animate-pulse rounded-xl mx-4" />
        }
        rootMargin="400px 0px"
      >
        <ReportsShowcase />
      </LazySection>
    </>
  );
};

// ✅ Stage 4: Heavy features content with viewport-based loading
const Stage4Content = () => {
  return (
    <>
      <LazySection
        fallback={
          <div className="h-[60vh] bg-gradient-to-b from-white/5 to-white/10 animate-pulse rounded-xl mx-4 mb-8" />
        }
        rootMargin="600px 0px"
      >
        <FeaturesHowItWorksSection />
      </LazySection>

      <LazySection
        fallback={
          <div className="h-[60vh] bg-gradient-to-b from-white/5 to-white/10 animate-pulse rounded-xl mx-4" />
        }
        rootMargin="600px 0px"
      >
        <FeaturesSection />
      </LazySection>
    </>
  );
};

// ✅ Stage 5: Bottom content with aggressive preloading
const Stage5Content = () => {
  return (
    <>
      <LazySection
        fallback={
          <div className="h-[60vh] bg-gradient-to-b from-white/5 to-white/10 animate-pulse rounded-xl mx-4 mb-8" />
        }
        rootMargin="800px 0px"
      >
        <LatestArticles />
      </LazySection>

      <LazySection
        fallback={
          <div className="h-[60vh] bg-gradient-to-b from-white/5 to-white/10 animate-pulse rounded-xl mx-4 mb-8" />
        }
        rootMargin="800px 0px"
      >
        <HomepageFAQSection />
      </LazySection>

      <LazySection
        fallback={
          <div className="h-[60vh] bg-gradient-to-b from-white/5 to-white/10 animate-pulse rounded-xl mx-4 mb-8" />
        }
        rootMargin="800px 0px"
      >
        <PricingSection />
      </LazySection>

      <LazySection
        fallback={
          <div className="h-[40vh] bg-gradient-to-b from-white/5 to-white/10 animate-pulse rounded-xl mx-4" />
        }
        rootMargin="800px 0px"
      >
        <FinalCTASection />
      </LazySection>
    </>
  );
};

// ✅ Stage 6: Non-critical scripts
const Stage6Content = () => {
  return (
    <LazySection fallback={<div />} rootMargin="200px 0px">
      <EnhancedSEOScript />
    </LazySection>
  );
};

// ✅ Main HomePage component
const HomePage = () => {
  return (
    <div className="relative isolate">
      {/* ✅ Essential scroll handling - loads immediately */}
      <HomePageScrollHandler />

      {/* ✅ CRITICAL: Above-the-fold content with SSR-safe Hero */}
      <HeroWrapper />

      {/* ✅ STAGE 1: Below-the-fold hero content */}
      {/* Bundle downloads only after 800ms delay */}
      <Stage1Content />

      {/* ✅ STAGE 2: Essential introductory content */}
      {/* Bundle downloads only after 1.5s delay */}
      <Stage2Content />

      {/* ✅ STAGE 3: Secondary content */}
      {/* Bundle downloads only after 3s delay OR user scroll past 400px */}
      <Stage3Content />

      {/* ✅ STAGE 4: Heavy features content */}
      {/* Bundle downloads only when user is 600px away from viewport */}
      <Stage4Content />

      {/* ✅ STAGE 5: Bottom content */}
      {/* Bundle downloads only when user is 800px away from viewport */}
      <Stage5Content />

      {/* ✅ STAGE 6: Non-critical scripts */}
      {/* Bundle downloads only when user is 200px away from bottom */}
      <Stage6Content />
    </div>
  );
};

export default HomePage;
