'use client';

import { useEffect } from 'react';

export default function GADebugger() {
  useEffect(() => {
    console.log('GA Debugger loaded');
    console.log(
      'GA Measurement ID:',
      process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID
    );

    // Check if gtag exists and log its contents
    if (typeof window !== 'undefined') {
      console.log('window.dataLayer:', window.dataLayer);
      console.log('window.gtag exists:', typeof window.gtag === 'function');

      // Check for consent cookie
      const consentCookie = document.cookie
        .split('; ')
        .find((row) => row.startsWith('user_consent_preferences='));

      if (consentCookie) {
        try {
          const consentData = JSON.parse(
            decodeURIComponent(consentCookie.split('=')[1])
          );
          console.log('Current consent state:', consentData);
        } catch (error) {
          console.log('Error parsing consent cookie:', error);
        }
      } else {
        console.log('No consent cookie found - user has not given consent yet');
      }
    }

    // Set up a listener for gtag calls
    if (typeof window !== 'undefined' && window.dataLayer) {
      const originalPush = window.dataLayer.push;
      window.dataLayer.push = function (...args) {
        // Log consent-related events with more detail
        if (
          args[0] &&
          typeof args[0] === 'object' &&
          args[0] !== null &&
          'event' in args[0] &&
          ((args[0] as Record<string, unknown>).event === 'consent_update' ||
            (args[0] as Record<string, unknown>).event === 'consent_default')
        ) {
          console.log('🍪 Consent event:', JSON.stringify(args, null, 2));
        } else {
          console.log('dataLayer.push:', JSON.stringify(args));
        }
        return originalPush.apply(this, args);
      };
    }
  }, []);

  return (
    <div className="fixed bottom-4 left-4 bg-black text-white p-2 text-xs z-50 rounded opacity-50 hover:opacity-100">
      GA Debug Active
    </div>
  );
}
