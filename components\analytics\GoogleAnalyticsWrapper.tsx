'use client';

import { GoogleAnalytics } from '@next/third-parties/google';
import { useEffect } from 'react';
import Script from 'next/script';

interface GoogleAnalyticsWrapperProps {
  gaId: string;
  gadsId?: string;
}

export default function GoogleAnalyticsWrapper({
  gaId,
  gadsId = process.env.NEXT_PUBLIC_GOOGLE_ADS_ID
}: GoogleAnalyticsWrapperProps) {
  useEffect(() => {
    console.log('Google Analytics initialized with ID:', gaId);

    if (gadsId) {
      console.log('Google Ads linked with ID:', gadsId);
    }

    // Wait for consent to be processed before sending events
    const timer = setTimeout(() => {
      if (typeof window !== 'undefined' && window.gtag) {
        // Send pageview event (will respect consent mode)
        window.gtag('event', 'page_view', {
          page_title: document.title,
          page_location: window.location.href,
          page_path: window.location.pathname
        });

        // Configure Google Ads if available
        if (gadsId) {
          window.gtag('config', gadsId, {
            send_page_view: false,
            link_google_analytics: gaId,
            allow_enhanced_conversions: true
          });
        }

        console.log('Pageview event sent with consent mode');
      }
    }, 200);

    return () => clearTimeout(timer);
  }, [gaId, gadsId]);

  return (
    <>
      <GoogleAnalytics gaId={gaId} />

      {/* Google Ads script with consent mode support */}
      {gadsId && (
        <>
          <Script
            id="google-ads-script"
            strategy="afterInteractive"
            src={`https://www.googletagmanager.com/gtag/js?id=${gadsId}`}
          />
          <Script
            id="google-ads-config"
            strategy="afterInteractive"
            dangerouslySetInnerHTML={{
              __html: `
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                gtag('js', new Date());

                gtag('config', '${gadsId}', {
                  send_page_view: true,
                  allow_enhanced_conversions: true,
                  link_attribution: {
                    cookie_expires: 2592000,
                    cookie_flags: 'SameSite=None;Secure'
                  }
                });
              `
            }}
          />
        </>
      )}
    </>
  );
}
