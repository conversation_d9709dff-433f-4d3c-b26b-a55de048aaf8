'use client';

import React, { useState, useEffect } from 'react';
import { CheckCircle2, Circle } from 'lucide-react';
import { IconWrapper } from '../ui/IconWrapper';

interface StepIndicatorProps {
  currentStep: number;
  steps: Array<{
    title: string;
    description: string;
  }>;
  onStepClick?: (stepIndex: number) => void;
}

export const FunnelStepIndicator: React.FC<StepIndicatorProps> = ({
  currentStep,
  steps,
  onStepClick
}) => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleStepClick = (index: number) => {
    if (index <= currentStep && onStepClick) {
      onStepClick(index);
    }
  };

  // Show a simplified version during SSR and hydration
  if (!mounted) {
    return (
      <div className="sticky top-16 z-50 backdrop-blur-sm py-3 px-4 sm:px-0 border-b border-border/40 mb-6 mt-12 sm:mt-0">
        <div className="hidden sm:flex justify-between items-center mx-auto">
          {steps.map((step, index) => (
            <React.Fragment key={index}>
              <div className="flex items-center gap-3">
                <div
                  className={`w-6 h-6 rounded-full border-2 flex-shrink-0
                  ${index === currentStep ? 'border-primary' : 'border-gray-300'}`}
                />
                <div className="flex flex-col min-w-0">
                  <span className="font-medium text-sm text-gray-400 truncate">
                    {step.title}
                  </span>
                </div>
              </div>
              {index < steps.length - 1 && (
                <div className="flex-1 h-px mx-4 bg-gray-200 max-w-[80px]" />
              )}
            </React.Fragment>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="sticky top-16 z-50 backdrop-blur-sm py-3 sm:px-0 border-b border-white/10 mb-6 mt-12 sm:mt-0">
      {/* Desktop View - Horizontal Layout */}
      <div className="hidden sm:flex justify-between items-center max-w-4xl mx-auto">
        {steps.map((step, index) => (
          <React.Fragment key={index}>
            <div
              className={`flex items-center gap-3 group ${
                index <= currentStep ? 'cursor-pointer' : 'cursor-not-allowed'
              }`}
              onClick={() => handleStepClick(index)}
              role={index <= currentStep ? 'button' : undefined}
              tabIndex={index <= currentStep ? 0 : undefined}
              onKeyPress={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  handleStepClick(index);
                }
              }}
            >
              <div
                className={`flex items-center justify-center w-6 h-6 rounded-full border-2 flex-shrink-0
                transition-all duration-200
                ${
                  index < currentStep
                    ? 'border-hero-yellow bg-hero-yellow text-[#111827] group-hover:scale-110'
                    : index === currentStep
                      ? 'border-hero-yellow text-hero-yellow group-hover:bg-white/10'
                      : 'border-white/30 text-white/50'
                }`}
              >
                <IconWrapper size={14}>
                  {index < currentStep ? (
                    <CheckCircle2 className="h-3.5 w-3.5" />
                  ) : (
                    <Circle className="h-3.5 w-3.5" />
                  )}
                </IconWrapper>
              </div>
              <div className="flex flex-col min-w-0">
                <span
                  className={`font-medium text-sm leading-tight ${
                    index <= currentStep ? 'text-yellow-400' : 'text-white/50'
                  }`}
                >
                  {step.title}
                </span>
                <span
                  className={`text-xs leading-tight ${
                    index <= currentStep ? 'text-slate-300' : 'text-slate-500'
                  } truncate max-w-[120px]`}
                  title={step.description}
                >
                  {step.description}
                </span>
              </div>
            </div>
            {index < steps.length - 1 && (
              <div
                className={`flex-1 h-px mx-4 max-w-[80px] transition-colors duration-200 ${
                  index < currentStep ? 'bg-hero-yellow' : 'bg-white/20'
                }`}
              />
            )}
          </React.Fragment>
        ))}
      </div>

      {/* Mobile View - Compact Progress Bar */}
      <div className="sm:hidden">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <div
              className={`w-5 h-5 rounded-full border-2 flex items-center justify-center
              ${currentStep >= 0 ? 'border-hero-yellow bg-hero-yellow text-[#111827]' : 'border-white/30'}`}
            >
              <IconWrapper size={12}>
                <CheckCircle2 className="h-3 w-3" />
              </IconWrapper>
            </div>
            <span className="text-sm font-medium text-yellow-400">
              {steps[currentStep].title}
            </span>
          </div>
          <span className="text-xs text-slate-300">
            {currentStep + 1}/{steps.length}
          </span>
        </div>

        <div className="flex items-center gap-1">
          {steps.map((_, index) => (
            <div
              key={index}
              className={`flex-1 h-1.5 rounded-full transition-colors duration-300 ${
                index <= currentStep ? 'bg-hero-yellow' : 'bg-white/20'
              }`}
            />
          ))}
        </div>

        <div className="mt-2 text-xs text-slate-400 truncate">
          {steps[currentStep].description}
        </div>
      </div>
    </div>
  );
};

export default FunnelStepIndicator;
