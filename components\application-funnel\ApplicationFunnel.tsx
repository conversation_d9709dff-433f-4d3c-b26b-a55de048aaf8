'use client';

import React, {
  useState,
  useEffect,
  useCallback,
  useRef,
  useMemo
} from 'react';
import { Button } from '@/components/ui/button';
import { ArrowRight } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { toast } from '@/hooks/use-toast';
import { loadStripe } from '@stripe/stripe-js';
import UploadJobForm from '../jobs/UploadJobForm';
import ResumeSelection from './ResumeSelection';
import JobAnalysisDisplay from '../jobs/JobAnalysisDisplay';
import ProductSelection, { Product } from './ProductSelection';
import { FunnelStepIndicator } from './FunnelStepIndicator';

import ResultsDisplay from './ResultsDisplay';
import {
  FunnelState,
  Resume,
  BasicPlanResult,
  ProPlanResult,
  UltimatePlanResult,
  Job,
  PlanType
} from '@/app/types/globalTypes';
import { User } from '@supabase/supabase-js';
import { useCredits } from '@/hooks/useCredits';
import { createClient } from '@/app/utils/supabase/client';
import { JobSection } from '../dashboard/JobSection';
import { ResultsStep, ResultsStepRef } from './ResultsStep';

// We no longer need to generate UUIDs for job IDs as the database will assign numeric IDs

interface ApplicationFunnelProps {
  user: User;
  resume: Resume | null;
  isLoading: boolean;
  onSubmit: (job: Job) => Promise<Job>;
}

export default function ApplicationFunnel({
  user,
  resume,
  isLoading,
  onSubmit
}: Readonly<ApplicationFunnelProps>) {
  const router = useRouter();
  const [error, setError] = useState<string | null>(null);
  const { credits, fetchCredits } = useCredits();
  const isUpdatingRef = useRef(false);
  const resultsStepRef = useRef<ResultsStepRef>(null);
  const [savedJobs, setSavedJobs] = useState<Job[]>([]);
  const [shouldAutoGenerateResults, setShouldAutoGenerateResults] =
    useState(false);

  const getInitialState = useCallback(() => {
    if (typeof window !== 'undefined') {
      const savedData = localStorage.getItem('applicationFunnelData');
      if (savedData) {
        const parsedData = JSON.parse(savedData);
        return {
          ...parsedData,
          selectedResume: parsedData.selectedResume || resume,
          step: parsedData.step || 0,
          credits: credits,
          processingResults: false,
          resultError: null,
          showResults: false,
          selectedProduct: parsedData.selectedProduct as Product | null,
          selectedPlan:
            parsedData.selectedPlan ||
            (parsedData.selectedProduct
              ? parsedData.selectedProduct.planType
              : null),
          job: parsedData.job as Job | null
        };
      }
    }
    return {
      step: 0,
      job: null,
      selectedResume: resume,
      jobSaved: false,
      selectedProduct: null as Product | null,
      credits: 0,
      processingResults: false,
      resultError: null,
      results: null,
      generatedResults: null as
        | BasicPlanResult
        | ProPlanResult
        | UltimatePlanResult
        | null,
      showResults: false,
      analysisHistory: [],
      lastAnalysisTimestamp: null,
      forceReanalysis: false
    } as FunnelState;
  }, [credits, resume]);

  const [state, setState] = useState<FunnelState>(getInitialState());

  const resetFunnel = useCallback(() => {
    const currentCredits = state.credits;
    localStorage.clear();
    setState(() => ({
      ...getInitialState(),
      credits: currentCredits
    }));
    toast({
      title: 'Reset Complete',
      description: 'Application funnel has been completely reset'
    });
  }, [state.credits, getInitialState, setState]);

  useEffect(() => {
    setState(getInitialState());
  }, [getInitialState]);

  useEffect(() => {
    if (user) {
      fetchCredits();
    }
  }, [user, fetchCredits]);

  useEffect(() => {
    setState((prev) => ({
      ...prev,
      credits
    }));
  }, [credits]);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem(
        'applicationFunnelData',
        JSON.stringify({
          step: state.step,
          job: state.job,
          selectedResume: state.selectedResume,
          jobSaved: state.jobSaved,
          selectedProduct: state.selectedProduct,
          generatedResults: state.generatedResults,
          analysisHistory: state.analysisHistory,
          lastAnalysisTimestamp: state.lastAnalysisTimestamp
        })
      );
    }
  }, [state]);

  const handleJobSubmit = useCallback(
    async (
      description: string,
      link: string,
      forceAnalysis = false
    ): Promise<Job> => {
      setError(null);
      setState((prev) => ({ ...prev, forceReanalysis: forceAnalysis }));

      try {
        const result = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/analyze_job`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Accept: 'application/json'
            },
            body: JSON.stringify({
              jobDescription: description,
              jobLink: link
            })
          }
        );

        if (!result.ok) throw new Error('Failed to analyze job');

        const analysisResult = await result.json();

        const newJob: Job = {
          // Don't assign an ID - let the database generate it
          id: null as unknown as string, // Temporary ID that will be replaced by the database
          user_id: user.id,
          title: analysisResult.job_title,
          company: analysisResult.company_name,
          description,
          job_link: link,
          salary_range: analysisResult.salary_range,
          location: analysisResult.location,
          analysis_result: analysisResult,
          status: 'active',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        // Call the onSubmit prop after successful analysis
        const savedJob = await onSubmit(newJob);

        setState((prevState) => ({
          ...prevState,
          job: savedJob,
          step: 1,
          jobSaved: false,
          forceReanalysis: false,
          lastAnalysisTimestamp: Date.now(),
          analysisHistory: [
            {
              jobId: newJob.id,
              timestamp: Date.now()
            },
            ...prevState.analysisHistory.slice(0, 4)
          ]
        }));

        toast({
          title: 'Job analyzed successfully',
          description: 'New analysis complete. Please review and save.'
        });
        return newJob;
      } catch (error) {
        console.error('Error analyzing job:', error);
        setError('Failed to analyze the job. Please try again.');
        setState((prev) => ({ ...prev, forceReanalysis: false }));
        toast({
          title: 'Error',
          description: 'Failed to analyze the job. Please try again.',
          variant: 'destructive'
        });
        throw error;
      }
    },
    [user.id, onSubmit, setError, setState]
  );

  const handlePurchaseCredits = useCallback(async (packageId: string) => {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_URL}/api/credits/purchase`,
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            packageId,
            returnUrl: window.location.href // Include current funnel URL
          })
        }
      );

      if (!response.ok) throw new Error('Purchase failed');

      const { sessionId } = await response.json();
      const stripe = await loadStripe(
        process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!
      );

      if (stripe) {
        await stripe.redirectToCheckout({ sessionId });
      }
    } catch (error) {
      console.error('Error:', error);
      toast({
        title: 'Error',
        description: 'Failed to initiate credit purchase',
        variant: 'destructive'
      });
    }
  }, []);

  const handleJobDataChange = useCallback(
    (description: string, link: string) => {
      const newJob: Job = {
        // Don't assign an ID - let the database generate it
        id: null as unknown as string, // Temporary ID that will be replaced by the database
        user_id: user.id,
        title: null,
        company: null,
        description,
        job_link: link,
        salary_range: null,
        location: null,
        analysis_result: {
          job_title: null,
          company_name: null,
          location: null,
          job_type: null,
          salary_range: null,
          sections: [],
          job_link: link
        },
        status: 'active',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      setState((prevState) => ({
        ...prevState,
        job: newJob,
        jobSaved: false,
        generatedResults:
          prevState.generatedResults && prevState.job?.id !== newJob.id
            ? null
            : prevState.generatedResults
      }));
    },
    [user.id, setState]
  );

  // Store the number of steps to avoid circular dependency
  const TOTAL_STEPS = 5; // Hardcoded value based on the steps array length

  const handleNext = useCallback(() => {
    // Don't advance past the results step if we have results
    if (state.step === TOTAL_STEPS - 1 && state.generatedResults) {
      return;
    }

    // Check if we're moving from Plan step (index 3) to Results step (index 4)
    const isMovingToResultsStep = state.step === 3;

    // Set flag to auto-generate results if moving from Plan to Results
    if (isMovingToResultsStep) {
      console.log('Setting flag to auto-generate results');
      setShouldAutoGenerateResults(true);
    }

    // Proceed to the next step
    setState((prev) => ({
      ...prev,
      step: Math.min(prev.step + 1, TOTAL_STEPS - 1)
    }));
  }, [
    state.step,
    state.generatedResults,
    setState,
    setShouldAutoGenerateResults
  ]);

  const handleProductSelect = useCallback(
    (product: Product) => {
      if (!isUpdatingRef.current) {
        isUpdatingRef.current = true;

        console.log('Product selected:', {
          title: product.title,
          planType: product.planType,
          price: product.price,
          creditCost: product.creditCost
        });

        setState((prev) => {
          const planChanged =
            prev.generatedResults &&
            prev.generatedResults.planType !== product.planType;

          const newState = {
            ...prev,
            selectedProduct: product,
            // Drop the cache if the user picked a different plan
            generatedResults: planChanged ? null : prev.generatedResults
          };

          console.log('State after product selection:', {
            planType: newState.selectedProduct?.planType,
            creditCost: newState.selectedProduct?.creditCost,
            selectedProduct: {
              title: newState.selectedProduct?.title,
              planType: newState.selectedProduct?.planType
            }
          });

          return newState;
        });

        setTimeout(() => {
          isUpdatingRef.current = false;
        }, 100);
      }
    },
    [setState, isUpdatingRef]
  );

  const fetchJobs = useCallback(async () => {
    try {
      const supabase = createClient();
      const { data: jobs, error } = await supabase
        .from('user_jobs')
        .select('*')
        .eq('user_id', user.id)
        .eq('status', 'saved') // Add status filter if needed
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching jobs:', error);
        return;
      }

      console.log('Funnel - Fetched jobs:', jobs); // Debug log
      setSavedJobs(jobs || []);
    } catch (error) {
      console.error('Error in fetchJobs:', error);
    }
  }, [user.id]);

  useEffect(() => {
    fetchJobs();
  }, [user.id, state.jobSaved, fetchJobs]); // Add fetchJobs to dependency array

  useEffect(() => {
    const logJobStatuses = () => {
      console.log(
        'Funnel jobs:',
        savedJobs.map((job) => ({
          id: job.id,
          status: job.status,
          title: job.title,
          created_at: job.created_at
        }))
      );
    };

    logJobStatuses();
  }, [savedJobs]);

  const steps = useMemo(
    () => [
      {
        title: 'Add Job',
        description: 'Paste job description or select existing job',
        content: (
          <div className="space-y-6">
            <div className="mb-8">
              <JobSection
                savedJobs={savedJobs}
                selectedJob={state.job}
                onViewDetails={(job) => {
                  setState((prev) => ({ ...prev, job }));
                }}
                onDelete={async (job) => {
                  const supabase = createClient();
                  await supabase.from('user_jobs').delete().eq('id', job.id);
                  fetchJobs();
                }}
                onSelect={(job) => {
                  // Update job in state
                  setState((prev) => ({
                    ...prev,
                    job,
                    jobSaved: true,
                    step: prev.step
                  }));
                }}
                // Don't pass onUploadJob prop to hide the Add Job button in the funnel
              />
            </div>
            <UploadJobForm
              onJobDataChange={handleJobDataChange}
              onSubmit={handleJobSubmit}
              onComplete={(saved) => {
                console.log('Job saved:', saved);
                /* nothing extra to do here – state update happens inside handleJobSubmit */
              }}
              initialJob={state.job ?? null}
              submitButtonText="Analyze Job"
              isLoading={isLoading}
            />
          </div>
        ),
        canProceed: () => state.job?.analysis_result != null
      },
      {
        title: 'Review',
        description: 'Check job analysis',
        content: (
          <div className="space-y-6">
            {isLoading ? (
              <div className="flex flex-col items-center justify-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[hsl(var(--hero-yellow))]" />
                <p className="mt-4 text-white">Analyzing job details...</p>
              </div>
            ) : state.job?.analysis_result ? (
              <JobAnalysisDisplay analysisResult={state.job.analysis_result} />
            ) : (
              <div className="p-8 backdrop-blur-sm bg-white/10 border border-white/20 rounded-lg text-white text-center shadow-lg">
                <p className="mb-2">No analysis result available.</p>
                <p className="text-sm text-slate-300">
                  Please go back and select or add a job to analyze.
                </p>
              </div>
            )}
          </div>
        ),
        canProceed: () => state.job?.analysis_result != null && !isLoading
      },
      {
        title: 'CV',
        description: 'Select your CV',
        content: (
          <div className="space-y-6">
            <ResumeSelection
              user={user}
              onComplete={(resume) => {
                setState((prev) => ({
                  ...prev,
                  selectedResume: resume,
                  generatedResults:
                    prev.generatedResults &&
                    prev.selectedResume?.id !== resume?.id
                      ? null
                      : prev.generatedResults
                }));
              }}
              selectedResume={state.selectedResume}
            />
          </div>
        ),
        canProceed: () => !!state.selectedResume
      },
      {
        title: 'Plan',
        description: 'Choose your package',
        content: (
          <ProductSelection
            onProductSelect={handleProductSelect}
            userCredits={state.credits}
            onPurchaseCredits={handlePurchaseCredits}
            user={user}
          />
        ),
        canProceed: () => state.selectedProduct !== null
      },
      {
        title: 'Results',
        description: 'View your insights',
        content: (
          <div className="space-y-6">
            <ResultsStep
              ref={resultsStepRef}
              state={state}
              setState={setState}
              user={user}
              isLoading={isLoading}
            />
          </div>
        ),
        canProceed: () => state.generatedResults !== null
      }
    ],
    [
      state,
      isLoading,
      savedJobs,
      handleJobDataChange,
      handleJobSubmit,
      fetchJobs,
      handleProductSelect,
      handlePurchaseCredits,
      user,
      resultsStepRef
    ]
  );

  const handleStepChange = useCallback(
    (stepIndex: number) => {
      const canNavigateToStep = steps
        .slice(0, stepIndex)
        .every((step) => step.canProceed());

      if (canNavigateToStep) {
        setState((prevState) => ({
          ...prevState,
          step: stepIndex
        }));
      } else {
        toast({
          title: 'Cannot Skip Steps',
          description: 'Please complete the current step before proceeding.',
          variant: 'destructive'
        });
      }
    },
    [steps, setState]
  );

  const renderCurrentStep = useCallback(() => {
    // Add debugging for the generatedResults state
    console.log('renderCurrentStep debug:', {
      hasGeneratedResults: !!state.generatedResults,
      resultType: state.generatedResults?.planType,
      selectedPlanType: state.selectedProduct?.planType,
      step: state.step,
      lastStep: steps.length - 1,
      hasJob: !!state.job,
      hasResume: !!state.selectedResume
    });

    // If we have results and we're on the results step, show the results
    if (
      state.generatedResults &&
      state.step === steps.length - 1 &&
      state.generatedResults.planType === state.selectedProduct?.planType
    ) {
      // Ensure we have a valid plan type by checking against the result's planType first
      const effectivePlanType =
        state.generatedResults.planType ||
        state.selectedProduct?.planType ||
        PlanType.BASIC;

      console.log('Rendering results with plan type:', effectivePlanType);
      console.log('Results object:', state.generatedResults);

      return (
        <ResultsDisplay
          results={state.generatedResults}
          selectedPlan={effectivePlanType as PlanType}
          job={state.job!}
          resume={state.selectedResume!}
        />
      );
    }

    // Otherwise, render the current step content directly
    return steps[state.step].content;
  }, [state, steps]);

  const canProceedToNextStep = useCallback(() => {
    // For debugging
    console.log('Current step:', state.step);
    console.log('Current step can proceed:', steps[state.step].canProceed());
    console.log('Selected CV:', state.selectedResume);

    return steps[state.step].canProceed();
  }, [state.step, steps, state.selectedResume]);

  useEffect(() => {
    console.log('ApplicationFunnel state updated:', {
      currentStep: state.step,
      stepName: steps[state.step]?.title,
      hasResults: !!state.generatedResults,
      selectedPlanType: state.selectedProduct?.planType
    });

    // Scroll to top of content when results are generated
    if (state.generatedResults) {
      // Use setTimeout to ensure the DOM has updated
      setTimeout(() => {
        const contentContainer = document.querySelector('.overflow-y-auto');
        if (contentContainer) {
          contentContainer.scrollTop = 0;
        }
      }, 100);
    }
  }, [state, state.generatedResults, steps]);

  // Auto-generate results when moving to the Results step
  useEffect(() => {
    const isOnResultsStep = state.step === 4; // Results is the last step (index 4)

    if (
      isOnResultsStep &&
      shouldAutoGenerateResults &&
      resultsStepRef.current &&
      !state.generatedResults
    ) {
      console.log('Auto-generating results after plan selection');
      // Small delay to ensure the component is fully rendered
      const timer = setTimeout(() => {
        console.log('Calling generateResults on resultsStepRef');
        resultsStepRef.current?.generateResults();
        // Reset the flag after triggering generation
        setShouldAutoGenerateResults(false);
      }, 500); // Increased delay to ensure component is fully rendered

      return () => clearTimeout(timer);
    }
  }, [state.step, shouldAutoGenerateResults, state.generatedResults]);

  useEffect(() => {
    console.log('Current state:', {
      step: state.step,
      hasGeneratedResults: !!state.generatedResults,
      generatedResultsType: state.generatedResults?.planType,
      selectedPlanType: state.selectedProduct?.planType,
      hasJob: !!state.job,
      hasResume: !!state.selectedResume
    });
  }, [state]);

  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const paymentSuccess = params.get('payment_success');

    if (paymentSuccess === 'true') {
      // Remove the query parameter
      window.history.replaceState({}, document.title, window.location.pathname);

      toast({
        title: 'Payment Successful',
        description: 'Credits have been added to your account',
        variant: 'default'
      });
    }
  }, []);

  return (
    <>
      <div className="relative isolate">
        {/* Using global background from LayoutWrapper instead of duplicating here */}

        <div className="mx-auto w-full max-w-[1200px] px-4">
          <FunnelStepIndicator
            currentStep={state.step}
            steps={steps.map(({ title, description }) => ({
              title,
              description
            }))}
            onStepClick={handleStepChange}
          />
        </div>

        <div className="flex flex-col min-h-[calc(100vh-13rem)] sm:min-h-[calc(100vh-15rem)]">
          <div className="mx-auto w-full max-w-[1200px] px-4 flex-1 overflow-y-auto scroll-smooth">
            {error && (
              <div className="mb-6 p-4 bg-red-500/20 backdrop-blur-sm border border-red-500/30 text-white rounded-lg">
                {error}
              </div>
            )}
            <div className="flex flex-col h-full">
              {/* Title Section - Always at top */}
              <div className="text-center space-y-4 my-4">
                <h2 className="font-roboto-condensed font-[700] leading-[0.9] tracking-[-0.05em] text-white text-[clamp(2.5rem,8vw,4.5rem)]">
                  {steps[state.step].title.split(' ').slice(0, -1).join(' ')}
                  <span className="text-yellow-400 text-shadow-yellow">
                    {' '}
                    {steps[state.step].title.split(' ').pop()}
                  </span>
                </h2>
                <p className="text-lg text-slate-200 max-w-2xl mx-auto">
                  {steps[state.step].description}
                </p>
              </div>

              {/* Content Section - Centered */}
              <div className="flex-1 flex flex-col">
                <div className="w-full flex-1">{renderCurrentStep()}</div>
              </div>
            </div>
          </div>
        </div>
        {/* Navigation Buttons - Now inside the content area */}
        <div className="w-full px-12 pt-4 backdrop-blur-sm  border-t border-white/10 sticky bottom-0">
          <div className="flex items-center justify-between">
            {/* Left side - Back and Continue buttons */}
            <div className="flex gap-4">
              <Button
                variant="outline"
                onClick={() => {
                  setState((prev) => {
                    const newStep = Math.max(0, prev.step - 1);
                    return {
                      ...prev,
                      step: newStep
                    };
                  });
                }}
                disabled={isLoading || state.processingResults}
                className="backdrop-blur-sm bg-white/10 border border-white/20 text-white hover:bg-white/20 hover:text-white"
              >
                Back
              </Button>

              {state.step === steps.length - 1 ? (
                state.generatedResults ? (
                  <Button
                    onClick={() => router.push(`/dashboard/${user.id}`)}
                    className="group px-8 py-3 bg-hero-yellow text-[#111827] font-semibold rounded-full shadow-lg flex items-center justify-center gap-3 hover:bg-hero-yellow-light active:scale-95"
                  >
                    Go to Dashboard
                    <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </Button>
                ) : null
              ) : (
                <Button
                  onClick={handleNext}
                  disabled={!canProceedToNextStep()}
                  className="group px-8 py-3 bg-hero-yellow text-[#111827] font-semibold rounded-full shadow-lg flex items-center justify-center gap-3 hover:bg-hero-yellow-light active:scale-95 disabled:opacity-50 disabled:pointer-events-none"
                >
                  Continue
                  <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </Button>
              )}
            </div>

            {/* Right side - Start again button */}
            {state.step > 0 && (
              <Button
                variant="outline"
                onClick={() => {
                  if (
                    window.confirm(
                      'Are you sure you want to reset the application process? All progress will be lost.'
                    )
                  ) {
                    resetFunnel();
                  }
                }}
                className="backdrop-blur-sm bg-white/10 border border-red-500/30 text-red-400 hover:bg-red-500/20 hover:text-red-300"
              >
                Start again
              </Button>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
