'use client';

import React, { useEffect, useState, useCallback } from 'react';
import { Check, Clock, Target } from '@/lib/icons';
import { useAnalytics } from '@/lib/analytics-manager';
import { useClientOnly } from '@/hooks/useClientOnly';
import type { CustomEventParams } from '@/types/events';

/**
 * Microsoft Clarity – custom-tag reference:
 * https://learn.microsoft.com/clarity/filters/custom-tags
 */

interface ClarityAPI {
  (command: 'event', name: string): void;
  (command: 'set', key: string, value: string): void;
  /** Present only while the stub is still queuing calls */
  q?: IArguments[];
}

// Client-only wrapper component for consent-dependent functionality
const ConsentAwareHero: React.FC = () => {
  const { trackCustomEvent } = useAnalytics();

  // Use dynamic import to avoid SSR issues
  const [ConsentComponent, setConsentComponent] =
    useState<React.ComponentType | null>(null);

  useEffect(() => {
    // Dynamically import the consent-dependent component
    import('@/hooks/useConsentManager')
      .then(({ useConsent }) => {
        const ConsentDependentHero = () => {
          const { consentState } = useConsent();
          return (
            <HeroSectionContent
              trackCustomEvent={trackCustomEvent}
              consentState={consentState}
            />
          );
        };
        setConsentComponent(() => ConsentDependentHero);
      })
      .catch(() => {
        // Fallback if consent provider is not available
        const FallbackHero = () => {
          const defaultConsentState = {
            analytics: false,
            advertising: false,
            functional: true,
            hasConsented: false
          };
          return (
            <HeroSectionContent
              trackCustomEvent={trackCustomEvent}
              consentState={defaultConsentState}
            />
          );
        };
        setConsentComponent(() => FallbackHero);
      });
  }, [trackCustomEvent]);

  if (!ConsentComponent) {
    // Show loading state during SSR and initial client render
    return (
      <HeroSectionContent
        trackCustomEvent={trackCustomEvent}
        consentState={null}
      />
    );
  }

  return <ConsentComponent />;
};

// Define proper types for the component props
interface ConsentState {
  analytics: boolean;
  advertising: boolean;
  functional: boolean;
  hasConsented: boolean;
}

// Main hero content component that receives consent state as props
const HeroSectionContent: React.FC<{
  trackCustomEvent: (
    eventName: string,
    parameters?: CustomEventParams,
    clarityTags?: Record<string, string>
  ) => void;
  consentState: ConsentState | null;
}> = ({ trackCustomEvent, consentState }) => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => setMounted(true), []);

  /* ---------- Clarity helpers ---------- */

  const getClarity = (): ClarityAPI | undefined =>
    typeof window !== 'undefined'
      ? (window as unknown as { clarity?: ClarityAPI }).clarity
      : undefined;

  /** True ⇢ real client loaded  · False ⇢ stub still queuing */
  const isClarityReady = useCallback((): boolean => {
    const c = getClarity();
    return (
      !!c &&
      typeof c === 'function' &&
      !('q' in c) && // stub sets a queue
      !c.toString().includes('push') // fallback check
    );
  }, []);

  /** Track with exponential back-off so calls aren’t lost */
  const trackWithClarity = useCallback(
    (eventName: string, tags: Record<string, string>, attempt = 0): void => {
      const MAX = 5;
      if (!consentState?.analytics) return;

      if (!isClarityReady()) {
        if (attempt < MAX) {
          const delay = 100 * 2 ** attempt; // 100-200-400-800-1600 ms
          setTimeout(
            () => trackWithClarity(eventName, tags, attempt + 1),
            delay
          );
        } else {
          console.warn(
            `[Clarity] Gave up after ${MAX} attempts – skipped "${eventName}"`
          );
        }
        return;
      }

      const clarity = getClarity()!;
      // 1️⃣ Attach tags
      Object.entries(tags).forEach(([k, v]) => {
        try {
          clarity('set', k, v);
        } catch (e) {
          console.warn(`[Clarity] tag "${k}" failed`, e);
        }
      });
      // 2️⃣ Send event
      try {
        clarity('event', eventName);
      } catch (e) {
        console.warn(`[Clarity] event "${eventName}" failed`, e);
      }
    },
    [consentState?.analytics, isClarityReady]
  );

  /* ---------- CTA click handlers ---------- */

  const trackGetStartedClick = () => {
    trackCustomEvent('get_started_clicked', {
      event_category: 'cta_engagement',
      event_label: 'homepage_hero_get_started',
      button_location: 'homepage_hero',
      button_text: 'Get Started Free',
      page_section: 'above_fold'
    });

    trackWithClarity('get_started_clicked', {
      conversion_intent: 'high',
      button_location: 'homepage_hero',
      cta_clicked: 'get_started_free'
    });
  };

  const trackATSCheckClick = () => {
    trackCustomEvent('ats_check_clicked', {
      event_category: 'cta_engagement',
      event_label: 'homepage_hero_ats_check',
      button_location: 'homepage_hero',
      button_text: 'Free ATS Check',
      page_section: 'above_fold'
    });

    trackWithClarity('ats_check_clicked', {
      conversion_intent: 'medium',
      button_location: 'homepage_hero',
      cta_clicked: 'free_ats_check'
    });
  };

  /* ---------- Page-view tracking ---------- */

  useEffect(() => {
    if (!mounted || !consentState?.analytics) return;

    trackCustomEvent('hero_section_viewed', {
      event_category: 'page_engagement',
      event_label: 'homepage_hero_viewed',
      page_section: 'above_fold'
    });

    trackWithClarity('hero_section_viewed', {
      page_section: 'above_fold',
      visitor_engagement: 'initial'
    });
  }, [mounted, consentState?.analytics, trackCustomEvent, trackWithClarity]);

  /* ---------- UI ---------- */

  const heroTextClass = mounted
    ? 'opacity-100 translate-y-0 transition-all duration-700 ease-out'
    : 'opacity-0 translate-y-6';

  const liveStats = [
    { label: 'CV Analysis', value: '< 60 s', icon: Clock },
    { label: 'ATS Score', value: '95%+', icon: Target },
    { label: 'UK Optimized', value: '100%', icon: Check }
  ];

  return (
    <section id="hero" className="relative w-full isolate overflow-visible">
      <div className="relative z-10 min-h-[calc(100vh-64px)] flex flex-col justify-center">
        <div className="mx-auto w-full max-w-5xl px-4 sm:px-6 lg:px-8 py-12">
          {/* banner */}
          <div className={`${heroTextClass} mb-6 flex justify-center`}>
            <div className="inline-flex items-center gap-3 bg-gradient-to-r from-hero-yellow/10 to-orange-400/10 backdrop-blur-sm border border-hero-yellow/30 rounded-full px-6 py-3 text-hero-yellow text-xs sm:text-sm font-semibold shadow-lg">
              <span className="w-4 h-4 bg-purple-400 rounded animate-pulse" />
              <span>AI-Powered Career Tools for UK Professionals</span>
              <span className="w-4 h-4 bg-yellow-400 rounded animate-pulse" />
            </div>
          </div>

          {/* headings */}
          <div className={`${heroTextClass} mb-8 text-center`}>
            <h1 className="font-roboto-condensed mb-4 font-bold leading-[0.85] tracking-[-0.02em] text-white drop-shadow-2xl text-4xl md:text-8xl lg:text-8xl">
              AI CV Generator &<br />
              <span className="text-yellow-400 text-shadow-yellow">
                Mock Interview Practice
              </span>
            </h1>
            <h2 className="mb-6 font-semibold leading-[1.1] text-slate-200 text-xl sm:text-2xl md:text-3xl">
              Land your next job with our AI-powered job-prep platform for UK
              professionals
            </h2>
            <p className="text-lg text-slate-300 max-w-3xl mx-auto leading-relaxed mb-8">
              JobSpaceAI isn&apos;t a job board — we&apos;re your AI career
              coach. Get ATS-ready CVs, practise interviews, and win the jobs
              you find elsewhere.
            </p>

            {/* CTAs */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <a
                href="/onboarding"
                onClick={trackGetStartedClick}
                className="inline-flex items-center justify-center px-8 py-4 text-lg font-bold text-black bg-hero-yellow hover:bg-yellow-300 rounded-xl transition-colors"
              >
                Get Started Free
              </a>
              <a
                href="/ats-resume-checker"
                onClick={trackATSCheckClick}
                className="inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white border border-white/20 hover:bg-white/10 rounded-xl transition-colors"
              >
                Free ATS Check
              </a>
            </div>
          </div>

          {/* live stats */}
          <div className={`${heroTextClass} flex justify-center`}>
            <div className="grid grid-cols-3 gap-8 bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
              {liveStats.map(({ icon: Icon, value, label }) => (
                <div key={label} className="text-center">
                  <div className="flex justify-center mb-2">
                    <Icon className="w-6 h-6 text-hero-yellow" />
                  </div>
                  <div className="text-2xl font-bold text-white mb-1">
                    {value}
                  </div>
                  <div className="text-sm text-slate-400">{label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

// Export the consent-aware hero component
export default ConsentAwareHero;
