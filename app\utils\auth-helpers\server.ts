'use server';

import { createClient } from '@/app/utils/supabase/server';
import { revalidatePath } from 'next/cache';
import { cookies } from 'next/headers';
import { getURL } from '../helpers';

// Helper function to validate email format
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export async function getUser() {
  const supabase = await createClient();
  const { data, error } = await supabase.auth.getUser();
  if (error || !data.user) {
    return null;
  }
  return data.user;
}

// Helper function to create error redirect URL
function getErrorRedirect(
  path: string,
  error: string,
  errorDescription: string
): string {
  const params = new URLSearchParams();
  params.set('error', error);
  params.set('error_description', errorDescription);
  return `${path}?${params.toString()}`;
}

// Helper function to create status redirect URL
function getStatusRedirect(
  path: string,
  status: string,
  statusDescription: string
): string {
  const params = new URLSearchParams();
  params.set('status', status);
  params.set('status_description', statusDescription);
  return `${path}?${params.toString()}`;
}

export async function requestPasswordUpdate(formData: FormData) {
  const email = String(formData.get('email')).trim();

  if (!isValidEmail(email)) {
    return getErrorRedirect(
      '/signin/forgot_password',
      'Invalid email address.',
      'Please try again.'
    );
  }

  const supabase = await createClient();
  const { error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: getURL('/auth/reset_password')
  });

  if (error) {
    // Check for specific error types
    if (error.message.toLowerCase().includes('rate limit')) {
      return getErrorRedirect(
        '/signin/forgot_password',
        'Rate limit exceeded',
        'You have requested too many password resets. Please wait 60 minutes before trying again.'
      );
    } else {
      return getErrorRedirect(
        '/signin/forgot_password',
        'Password reset failed',
        error.message
      );
    }
  }

  // Add the email to the redirect URL as a parameter
  const redirectUrl = getStatusRedirect(
    '/signin/forgot_password',
    'Success!',
    'Please check your email for a password reset link. The link is only valid for 10 minutes.'
  );

  // Append the email parameter to the URL
  const url = new URL(redirectUrl, getURL());
  url.searchParams.set('email_sent', email);

  return url.toString();
}

export async function updatePassword(formData: FormData) {
  const password = String(formData.get('password')).trim();
  const passwordConfirm = String(formData.get('passwordConfirm')).trim();

  // Validate password length
  if (password.length < 6) {
    return getErrorRedirect(
      '/signin/update_password',
      'Password too short',
      'Your password must be at least 6 characters long.'
    );
  }

  // Validate password match
  if (password !== passwordConfirm) {
    return getErrorRedirect(
      '/signin/update_password',
      'Passwords do not match',
      'Please ensure both password fields contain the same value.'
    );
  }

  try {
    const supabase = await createClient();

    // First verify that the user is authenticated using getUser()
    const { data: userData, error: userError } = await supabase.auth.getUser();

    if (userError || !userData.user) {
      console.error('User verification error:', userError);
      return getErrorRedirect(
        '/signin/update_password',
        'Authentication error',
        'Unable to verify your identity. Please try again or request a new password reset link.'
      );
    }

    // Now update the password for the verified user
    const { error } = await supabase.auth.updateUser({
      password
    });

    if (error) {
      console.error('Password update error:', error);
      return getErrorRedirect(
        '/signin/update_password',
        'Password update failed',
        error.message
      );
    }

    // Password updated successfully
    return getStatusRedirect(
      '/signin/password_signin',
      'Password updated successfully!',
      'Your password has been changed. You can now sign in with your new password.'
    );
  } catch (error) {
    console.error('Unexpected error during password update:', error);
    return getErrorRedirect(
      '/signin/update_password',
      'An unexpected error occurred',
      error instanceof Error ? error.message : 'Please try again later.'
    );
  }
}

export async function signInWithPassword(formData: FormData) {
  const email = String(formData.get('email')).trim();
  const password = String(formData.get('password')).trim();
  const next = formData.get('next')
    ? String(formData.get('next'))
    : '/dashboard';

  console.log('[signInWithPassword] next parameter:', next);

  const supabase = await createClient();

  // Sign in the user
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password
  });

  if (error || !data.session) {
    console.log('[signInWithPassword] signIn error:', error);
    return `/signin/password_signin?error=${encodeURIComponent('Sign in failed.')}&error_description=${encodeURIComponent(error ? error.message : 'No session created')}`;
  }

  // Set a cookie to track preferred sign-in method
  const cookieStore = await cookies();
  cookieStore.set('preferredSignInView', 'password_signin', {
    path: '/',
    maxAge: 60 * 60 * 24 * 30, // 30 days
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax'
  });

  // Set a flag to help the client know auth succeeded
  cookieStore.set('auth_success', 'true', {
    path: '/',
    maxAge: 30, // short-lived, just for the redirect
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax'
  });

  // Revalidate to clear any cached data
  revalidatePath('/', 'layout');

  // Determine where to redirect
  const redirectTo = next || '/dashboard';
  console.log('[signInWithPassword] redirecting to:', redirectTo);
  return `${redirectTo}?status=Success!&status_description=${encodeURIComponent('You are now signed in')}`;
}

export async function signUp(formData: FormData) {
  const email = String(formData.get('email')).trim();
  const password = String(formData.get('password')).trim();
  const next = formData.get('next')
    ? String(formData.get('next'))
    : '/dashboard';

  // Validate inputs server-side as well
  if (!email) {
    return `/signin/signup?error=${encodeURIComponent('Invalid email')}&error_description=${encodeURIComponent('Email is required')}`;
  }

  if (!isValidEmail(email)) {
    return `/signin/signup?error=${encodeURIComponent('Invalid email')}&error_description=${encodeURIComponent('Please enter a valid email address')}`;
  }

  if (!password) {
    return `/signin/signup?error=${encodeURIComponent('Invalid password')}&error_description=${encodeURIComponent('Password is required')}`;
  }

  if (password.length < 6) {
    return `/signin/signup?error=${encodeURIComponent('Invalid password')}&error_description=${encodeURIComponent('Password must be at least 6 characters')}`;
  }

  const supabase = await createClient();

  let userData = null;

  try {
    const { error, data } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: `${process.env.NEXT_PUBLIC_URL}/auth/callback`
      }
    });

    if (error) {
      console.error('Signup error:', error);
      return `/signin/signup?error=${encodeURIComponent('Sign up failed')}&error_description=${encodeURIComponent(error.message)}`;
    }

    // Store user data for later use
    userData = data.user;

    // Check if user was created but email confirmation is required
    if (data?.user?.identities?.length === 0) {
      return `/signin/signup?error=${encodeURIComponent('Email already registered')}&error_description=${encodeURIComponent('This email is already registered. Please sign in or use a different email.')}`;
    }

    // Initialize user credits
    if (userData) {
      try {
        // Check if user already has credits
        const { data: existingCredits, error: fetchError } = await supabase
          .from('credits')
          .select('*')
          .eq('user_id', userData.id)
          .maybeSingle();

        if (fetchError) {
          console.error('Error checking existing credits:', fetchError);
        }

        // If no credits exist, create initial credits record
        if (!existingCredits && !fetchError) {
          const { error: insertError } = await supabase.from('credits').insert([
            {
              user_id: userData.id,
              amount: 80, // Initial credits amount - consistent with dashboard
              updated_at: new Date().toISOString()
            }
          ]);

          if (insertError) {
            console.error('Error creating initial credits:', insertError);
            // Don't return error - continue with signup
          } else {
            console.log(
              'Successfully initialized credits for user:',
              userData.id
            );
          }
        }
      } catch (creditsError) {
        console.error('Unexpected error initializing credits:', creditsError);
        // Don't return error - continue with signup
      }
    }

    // Set cookies for successful signup
    const cookieStore = await cookies();
    cookieStore.set('preferredSignInView', 'password_signin', {
      path: '/',
      maxAge: 60 * 60 * 24 * 30, // 30 days
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax'
    });

    // ✅ ADD CONVERSION TRACKING COOKIE
    cookieStore.set('signup_success', 'true', {
      path: '/',
      maxAge: 120, // 2 minutes - gives time for page to load and track
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax'
    });

    console.log('🎯 Signup success - tracking cookie set');

    // Revalidate to clear any cached data
    revalidatePath('/', 'layout');

    // FIXED: Redirect to signin page with success message instead of dashboard
    // This ensures the user sees the success message and doesn't get lost in redirects
    return `/signin/password_signin?status=Success!&status_description=${encodeURIComponent('Please check your email to confirm your account.')}&signup=true&next=${encodeURIComponent(next)}`;
  } catch (error) {
    console.error('Unexpected error during signup:', error);
    return `/signin/signup?error=${encodeURIComponent('Sign up failed')}&error_description=${encodeURIComponent('An unexpected error occurred. Please try again later.')}`;
  }
}

export async function signInWithEmail(formData: FormData) {
  const email = String(formData.get('email')).trim();
  const next = formData.get('next')
    ? String(formData.get('next'))
    : '/dashboard';

  const supabase = await createClient();

  const { error } = await supabase.auth.signInWithOtp({
    email,
    options: {
      emailRedirectTo: `${process.env.NEXT_PUBLIC_URL}/auth/callback`
    }
  });

  if (error) {
    return `${next}?error=${encodeURIComponent('Sign in failed.')}&error_description=${encodeURIComponent(error.message)}`;
  }

  const cookieStore = await cookies();
  cookieStore.set('preferredSignInView', 'email_signin', { path: '/' });
  return `${next}?status=Success!&status_description=${encodeURIComponent('Please check your email for a magic link.')}`;
}

export async function signOut() {
  'use server';

  const cookieStore = await cookies();
  const supabase = await createClient();

  // Sign out using Supabase auth API
  await supabase.auth.signOut();

  // Explicitly clear all auth-related cookies from the browser
  const authCookies = cookieStore
    .getAll()
    .filter(
      (cookie: { name: string }) =>
        cookie.name.includes('sb-') || cookie.name.includes('supabase-auth')
    );

  authCookies.forEach((cookie: { name: string }) => {
    cookieStore.delete(cookie.name);
  });

  // Return the URL to redirect to
  return '/';
}
