// components/ClientComponents.tsx
'use client';

import { ReactNode, useEffect, useState, ComponentType } from 'react';
import dynamic from 'next/dynamic';
import { Toaster } from './ui/toaster';

// ✅ CORE COMPONENTS - Load immediately
import { ConsentProvider } from '@/hooks/useConsentManager';
import { ConsentBanner } from '@/components/consent/ConsentBanner';

// ✅ Component type definitions for dynamic imports
type DynamicComponentType = ComponentType<Record<string, unknown>>;

// ✅ HEAVY COMPONENTS - Dynamic loading with inline object literals
const SessionRefresher = dynamic(
  (): Promise<{ default: DynamicComponentType }> =>
    import('@/components/SessionRefresher'),
  {
    ssr: false,
    loading: () => null
  }
);

const GADebugger = dynamic(
  (): Promise<{ default: DynamicComponentType }> =>
    import('@/components/analytics/GADebugger'),
  {
    ssr: false,
    loading: () => null
  }
);

interface GoogleAnalyticsWrapperProps {
  gaId: string;
  gadsId?: string;
}

const GoogleAnalyticsWrapper = dynamic(
  (): Promise<{ default: ComponentType<GoogleAnalyticsWrapperProps> }> =>
    import('@/components/analytics/GoogleAnalyticsWrapper'),
  {
    ssr: false,
    loading: () => null
  }
);

const WebLockReleaser = dynamic(
  (): Promise<{ default: DynamicComponentType }> =>
    import('@/components/components/WebLockReleaser'),
  {
    ssr: false,
    loading: () => null
  }
);

// ✅ GTM with proper typing
interface ConditionalGTMProps {
  gtmId?: string;
}

const ConditionalGTM = dynamic(
  (): Promise<{ default: ComponentType<ConditionalGTMProps> }> =>
    import('@/components/analytics/ConditionalGTM').then((mod) => ({
      default: mod.ConditionalGTM
    })),
  {
    ssr: false,
    loading: () => null
  }
);

// ✅ Traffic source tracker
const TrafficSourceTracker = dynamic(
  (): Promise<{ default: DynamicComponentType }> =>
    import('@/components/analytics/TrafficSourceTracker'),
  {
    ssr: false,
    loading: () => null
  }
);

// ✅ Signup conversion tracker
const SignupConversionTracker = dynamic(
  (): Promise<{ default: DynamicComponentType }> =>
    import('@/components/analytics/SignupConversionTracker').then((mod) => ({
      default: mod.SignupConversionTracker
    })),
  {
    ssr: false,
    loading: () => null
  }
);

interface ClientComponentsWrapperProps {
  children: ReactNode;
  gaId?: string;
  gadsId?: string;
}

interface AnalyticsState {
  isClient: boolean;
  analyticsReady: boolean;
  gtmReady: boolean;
  error: string | null;
}

export function ClientComponentsWrapper({
  children,
  gaId,
  gadsId = process.env.NEXT_PUBLIC_GOOGLE_ADS_ID
}: ClientComponentsWrapperProps): JSX.Element {
  const [state, setState] = useState<AnalyticsState>({
    isClient: false,
    analyticsReady: false,
    gtmReady: false,
    error: null
  });

  // ✅ Ensure client-side rendering
  useEffect(() => {
    setState((prev) => ({ ...prev, isClient: true }));
    console.log('🔧 ClientComponentsWrapper mounted on client');
  }, []);

  // ✅ Analytics initialization with error handling
  useEffect(() => {
    if (!state.isClient || !gaId) return;

    const initializeAnalytics = async (): Promise<void> => {
      try {
        // Check if analytics is already loaded
        if (window.gtag && window.dataLayer) {
          setState((prev) => ({ ...prev, analyticsReady: true }));
          return;
        }

        // Wait a bit for the layout scripts to load
        await new Promise<void>((resolve) => setTimeout(resolve, 500));

        if (window.gtag && window.dataLayer) {
          setState((prev) => ({ ...prev, analyticsReady: true }));
          console.log('📊 Analytics ready');
        }
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Unknown analytics error';
        console.warn('Analytics initialization error:', errorMessage);
        setState((prev) => ({ ...prev, error: errorMessage }));
      }
    };

    void initializeAnalytics();
  }, [state.isClient, gaId]);

  // ✅ GTM readiness check
  useEffect(() => {
    if (!state.isClient) return;

    const checkGTM = (): void => {
      if (window.dataLayer && Array.isArray(window.dataLayer)) {
        setState((prev) => ({ ...prev, gtmReady: true }));
      }
    };

    // Check immediately and then periodically
    checkGTM();
    const interval = setInterval(checkGTM, 1000);

    // Cleanup after 10 seconds max
    const timeout = setTimeout(() => {
      clearInterval(interval);
    }, 10000);

    return () => {
      clearInterval(interval);
      clearTimeout(timeout);
    };
  }, [state.isClient]);

  // ✅ Don't render anything until client-side hydration
  if (!state.isClient) {
    return <>{children}</>;
  }

  // ✅ Show error state if analytics failed to load
  if (state.error && process.env.NODE_ENV === 'development') {
    console.warn(`Analytics Error: ${state.error}`);
  }

  return (
    <ConsentProvider>
      <div>
        {children}

        {/* ✅ CORE COMPONENTS - Always load */}
        <WebLockReleaser />
        <SessionRefresher />
        <Toaster />

        {/* ✅ ANALYTICS - Load conditionally and in sequence */}
        {gaId && (
          <>
            {/* GTM loads first */}
            <ConditionalGTM />

            {/* GA wrapper loads after GTM is ready */}
            {state.analyticsReady && (
              <GoogleAnalyticsWrapper gaId={gaId} gadsId={gadsId} />
            )}

            {/* Debug only in development and after analytics is ready */}
            {process.env.NODE_ENV === 'development' && state.analyticsReady && (
              <GADebugger />
            )}
          </>
        )}

        {/* ✅ TRACKING COMPONENTS - Load after analytics */}
        {process.env.NEXT_PUBLIC_CLARITY_PROJECT_ID && state.analyticsReady && (
          <TrafficSourceTracker />
        )}

        {/* ✅ CONVERSION TRACKING - Load after analytics */}
        {state.analyticsReady && <SignupConversionTracker />}

        {/* ✅ CONSENT BANNER - Always last */}
        <ConsentBanner />
      </div>
    </ConsentProvider>
  );
}

// ✅ Export types for other components to use
export type { ClientComponentsWrapperProps };
