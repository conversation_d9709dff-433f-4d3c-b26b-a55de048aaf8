import './globals.css';
import { constructMetadata } from '@/lib/seo-config';
import localFont from 'next/font/local';
import { Inter } from 'next/font/google';
import type { Metadata } from 'next';

// ✅ CRITICAL: Direct imports for server components
import { ClientComponentsWrapper } from '@/components/ClientComponents';
import LayoutWrapper from '@/components/layout/LayoutWrapper';
import { TooltipProvider } from '@/components/ui/tooltip';
import { DynamicLayoutEnhancements } from '@/components/layout/DynamicLayoutEnhancements';

// ✅ Font optimization - Reduced to bare minimum
const inter = Inter({
  subsets: ['latin'],
  variable: '--font-sans',
  display: 'swap',
  weight: ['400', '600'],
  preload: true,
  fallback: ['system-ui', 'arial']
});

const geistSans = localFont({
  src: './fonts/GeistVF.woff',
  variable: '--font-geist-sans',
  weight: '100 900',
  display: 'swap',
  preload: false,
  fallback: ['system-ui', 'arial']
});

export const metadata: Metadata = {
  metadataBase: new URL('https://jobspaceai.com'),
  ...constructMetadata({
    path: '/'
  }),
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon-16x16.png',
    apple: '/apple-touch-icon.png'
  }
};

export default function RootLayout({
  children
}: {
  children: React.ReactNode;
}) {
  // ✅ Safely handle environment variables
  const clarityProjectId = process.env.NEXT_PUBLIC_CLARITY_PROJECT_ID;
  const gaId = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID;

  return (
    <html lang="en" className="dark dark-theme" suppressHydrationWarning>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />

        {/* ✅ OPTIMIZED: Minimal DNS prefetch - only when needed */}
        <link rel="dns-prefetch" href="//ylsypkzjfqwwaheobapi.supabase.co" />
        <link
          rel="preconnect"
          href="https://ylsypkzjfqwwaheobapi.supabase.co"
          crossOrigin="anonymous"
        />

        {/* ✅ DNS prefetch for analytics (only if IDs exist) */}
        {gaId && (
          <>
            <link rel="dns-prefetch" href="//www.googletagmanager.com" />
            <link rel="dns-prefetch" href="//www.google-analytics.com" />
          </>
        )}

        {clarityProjectId && (
          <>
            <link rel="dns-prefetch" href="//www.clarity.ms" />
            <link
              rel="preconnect"
              href="https://www.clarity.ms"
              crossOrigin="anonymous"
            />
          </>
        )}

        {/* ✅ Critical CSS - optimized further */}
        <style
          dangerouslySetInnerHTML={{
            __html: `
            html{scroll-behavior:smooth}
            body{margin:0;padding:0;overflow-x:hidden;background-color:rgb(1,6,11)}
            .layout-loading{background:linear-gradient(to bottom,rgb(1,6,11)0%,rgba(1,6,11,0.7)10%,transparent 35%,transparent 75%,rgba(1,6,11,0.7)90%,rgba(1,6,11,1)100%);min-height:100vh}
            `
          }}
        />

        {/* ✅ Microsoft Clarity - Only if project ID exists */}
        {clarityProjectId && (
          <script
            type="text/javascript"
            dangerouslySetInnerHTML={{
              __html: `
                (function(c,l,a,r,i,t,y){
                  c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
                  t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
                  y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
                })(window, document, "clarity", "script", "${clarityProjectId}");
              `
            }}
          />
        )}

        <meta name="bfcache-eligible" content="true" />
      </head>
      <body
        className={`${inter.variable} ${geistSans.variable} min-h-screen font-sans antialiased dark-theme`}
      >
        {/* ✅ IMPROVED: Minimal consent setup with better error handling */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              
              // Minimal consent setup - just the essentials
              gtag('consent', 'default', {
                analytics_storage: 'denied',
                ad_storage: 'denied',
                ad_user_data: 'denied',
                ad_personalization: 'denied',
                functionality_storage: 'granted',
                security_storage: 'granted',
                wait_for_update: 2000
              });
              
              // Make gtag globally available
              window.gtag = gtag;
              
              // Add error handling for analytics
              window.addEventListener('unhandledrejection', function(event) {
                if (event.reason && event.reason.toString().includes('googletagmanager')) {
                  console.warn('Google Analytics request blocked or failed');
                  event.preventDefault();
                }
              });
            `
          }}
        />

        {/* ✅ Main content wrapper */}
        <div
          className="relative min-h-screen"
          style={{ backgroundColor: 'transparent' }}
        >
          <div
            className="absolute inset-0 w-full pointer-events-none z-0"
            style={{
              background:
                'linear-gradient(to bottom, rgb(1, 6, 11) 0%, rgba(1, 6, 11,0.7) 10%, transparent 35%, transparent 75%, rgba(1, 6, 11,0.7) 90%, rgba(1, 6, 11,1) 100%)'
            }}
          />

          <DynamicLayoutEnhancements />

          <ClientComponentsWrapper gaId={gaId}>
            <TooltipProvider delayDuration={300}>
              <main
                className="relative z-10 pt-10"
                style={{ backgroundColor: 'transparent' }}
              >
                <div
                  style={{ backgroundColor: 'transparent', minHeight: '100vh' }}
                >
                  <LayoutWrapper>{children}</LayoutWrapper>
                </div>
              </main>
            </TooltipProvider>
          </ClientComponentsWrapper>
        </div>
      </body>
    </html>
  );
}
