'use client';

import Script from 'next/script';
import { useEffect, useState } from 'react';

export function ConditionalGTM() {
  const [shouldLoadGTM, setShouldLoadGTM] = useState(false);

  useEffect(() => {
    let isLoaded = false;

    // Function to load GTM
    const loadGTM = () => {
      if (!isLoaded) {
        isLoaded = true;
        setShouldLoadGTM(true);
        console.log('🏷️ GTM triggered to load');
      }
    };

    // Load after 3 seconds (fallback)
    const timer = setTimeout(loadGTM, 3000);

    // Or load immediately on first interaction
    const handleInteraction = () => {
      clearTimeout(timer);
      loadGTM();
    };

    // Listen for user interactions
    const events = ['scroll', 'mousedown', 'touchstart', 'keydown'];
    events.forEach((event) => {
      document.addEventListener(event, handleInteraction, {
        once: true,
        passive: true
      });
    });

    return () => {
      clearTimeout(timer);
      events.forEach((event) => {
        document.removeEventListener(event, handleInteraction);
      });
    };
  }, []); // Empty dependency array - no dependencies

  if (!shouldLoadGTM) {
    return null;
  }

  return (
    <>
      <Script
        id="gtm-script"
        strategy="afterInteractive"
        onLoad={() => console.log('🏷️ GTM script loaded successfully')}
        onError={(e) => console.error('❌ GTM script failed to load:', e)}
        dangerouslySetInnerHTML={{
          __html: `
            console.log('🏷️ GTM script executing...');
            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            console.log('🏷️ GTM script injection complete');
            })(window,document,'script','dataLayer','GTM-TVM2279Z');
          `
        }}
      />

      <noscript>
        <iframe
          src="https://www.googletagmanager.com/ns.html?id=GTM-TVM2279Z"
          height="0"
          width="0"
          style={{ display: 'none', visibility: 'hidden' }}
        />
      </noscript>
    </>
  );
}
